<script>
	import { onMount, onDestroy } from 'svelte';
	import { truncateText, generateAnimeUrl } from '$lib/myUtils';
	import { Button } from '$lib/components/ui/button';
	import { Play, Info, Star, TrendingUp, List, ChevronLeft, ChevronRight, X } from 'lucide-svelte';
	import { fade, slide } from 'svelte/transition';
	import * as ContextMenu from '$lib/components/ui/context-menu';
	import { goto } from '$app/navigation';
	import { Skeleton } from '$lib/components/ui/skeleton';
	import { getPreferredTitle } from '$lib/utils/titleHelper';
	import { browser } from '$app/environment';

	// Props from parent
	export let data, userSettings;
	let preferRomaji;
	if (!userSettings?.titleLanguage) {
		preferRomaji = true;
	} else {
		preferRomaji = userSettings.titleLanguage === 'romaji';
	}
	let isReady = false;
	let showMobileInfo = false;
	let currentAnime;
	let contextMenuOpen = false;
	let isMobileLandscape = false;
	let startX;
	let currentX;
	let isDragging = false;
	let currentSlide = 0;
	let sliderInterval;
	let progressValue = 0;
	let sliderContainer;
	const threshold = 50;
	const SLIDE_DURATION = 10000;
	let showDiscordNotification = true;

	const genreTranslations = {
		Action: 'Akcja',
		Adventure: 'Przygodowe',
		Comedy: 'Komedia',
		Drama: 'Dramat',
		Fantasy: 'Fantasy',
		Horror: 'Horror',
		Mystery: 'Mystery',
		Romance: 'Romans',
		'Sci-Fi': 'Sci-Fi',
		'Slice of Life': 'Slice of Life',
		Sports: 'Sport',
		Supernatural: 'Nadprzyrodzone',
		Thriller: 'Thriller',
		Mecha: 'Mecha',
		Psychological: 'Psychologiczne',
		Music: 'Muzyczne',
		School: 'Szkolne',
		Seinen: 'Seinen',
		Shounen: 'Shounen',
		Shoujo: 'Shoujo',
		'Martial Arts': 'Sztuki Walki',
		Historical: 'Historyczne',
		Military: 'Militarne',
		Demons: 'Demony',
		Magic: 'Magia',
		Harem: 'Harem',
		Ecchi: 'Ecchi',
		Isekai: 'Isekai',
		Game: 'Gry',
		Parody: 'Parodia',
		Police: 'Policyjne',
		Space: 'Kosmos',
		Vampire: 'Wampiry'
	};

	function goToSlide(index) {
		currentSlide = index;
		resetProgress();
	}

	// Use heroData directly from server instead of processing it here
	$: heroItems = data.heroData;

	// Add a Map to track loading state of images
	let imageLoadingStates = new Map();

	// Function to handle image load
	function handleImageLoad(index) {
		imageLoadingStates.set(index, true);
		imageLoadingStates = imageLoadingStates; // Trigger reactivity
	}

	function handleTouchStart(e) {
		startX = e.touches[0].clientX;
		isDragging = true;
	}

	function handleTouchMove(e) {
		if (!isDragging) return;
		currentX = e.touches[0].clientX;
		const diff = startX - currentX;
		if (Math.abs(diff) > threshold) {
			if (diff > 0) {
				nextSlide();
			} else {
				prevSlide();
			}
			isDragging = false;
		}
	}

	function handleTouchEnd() {
		isDragging = false;
	}

	function handleMouseDown(e) {
		startX = e.clientX;
		isDragging = true;
	}

	function handleMouseMove(e) {
		if (!isDragging) return;
		currentX = e.clientX;
		const diff = startX - currentX;
		if (Math.abs(diff) > threshold) {
			if (diff > 0) {
				nextSlide();
			} else {
				prevSlide();
			}
			isDragging = false;
		}
	}

	function handleMouseUp() {
		isDragging = false;
	}

	function nextSlide() {
		currentSlide = (currentSlide + 1) % heroItems.length;
		resetProgress();
	}

	function prevSlide() {
		currentSlide = (currentSlide - 1 + heroItems.length) % heroItems.length;
		resetProgress();
	}

	function resetProgress() {
		progressValue = 0;
		clearInterval(sliderInterval);
		startProgressAnimation();
	}

	function startProgressAnimation() {
		sliderInterval = setInterval(() => {
			progressValue += 100 / (SLIDE_DURATION / 100);
			if (progressValue >= 100) {
				nextSlide();
			}
		}, 100);
	}

	function handleNavigation(anime, episode, event, newTab = false) {
		const url = episode ? `${generateAnimeUrl(anime)}/watch/${episode}` : generateAnimeUrl(anime);

		if (newTab || (event && event.button === 1)) {
			window.open(url, '_blank');
		} else {
			goto(url, { noScroll: true });
		}

		contextMenuOpen = false;
	}

	function handleContextMenu(anime) {
		currentAnime = anime;
	}

	function handleContextMenuOpenChange(open) {
		contextMenuOpen = open;
	}

	function handleAddToWatchlist() {
		contextMenuOpen = false;
	}

	function handleShare() {
		contextMenuOpen = false;
	}

	function handleKeyDown(event, anime, episode) {
		if (event.key === 'Enter' || event.key === ' ') {
			event.preventDefault();
			handleNavigation(anime, episode, event);
		}
	}

	function toggleMobileInfo() {
		showMobileInfo = !showMobileInfo;
	}

	function checkMobileLandscape() {
		isMobileLandscape = window.innerWidth <= 9999 && window.innerHeight <= 500;
	}

	function dismissDiscordNotification() {
		showDiscordNotification = false;
		if (browser) {
			localStorage.setItem('discordNotificationDismissed', 'true');
		}
	}

	onMount(() => {
		isReady = true;
		startProgressAnimation();
		checkMobileLandscape();
		window.addEventListener('resize', checkMobileLandscape);

		// Check if notification was previously dismissed
		if (browser) {
			const dismissed = localStorage.getItem('discordNotificationDismissed');
			if (dismissed === 'true') {
				showDiscordNotification = false;
			}
		}

		// Preload the first hero image if available
		if (heroItems.length > 0) {
			const img = new Image();
			img.src = heroItems[0].latest_episode.thumbnail;
		}

		sliderContainer = document.querySelector('.slider-container');
		if (sliderContainer) {
			sliderContainer.addEventListener('touchstart', handleTouchStart, { passive: true });
			sliderContainer.addEventListener('touchmove', handleTouchMove, { passive: true });
			sliderContainer.addEventListener('touchend', handleTouchEnd, { passive: true });
			sliderContainer.addEventListener('mousedown', handleMouseDown);
		}
		window.addEventListener('mousemove', handleMouseMove);
		window.addEventListener('mouseup', handleMouseUp);
	});

	onDestroy(() => {
		clearInterval(sliderInterval);
		window.removeEventListener('resize', checkMobileLandscape);

		if (sliderContainer) {
			sliderContainer.removeEventListener('touchstart', handleTouchStart);
			sliderContainer.removeEventListener('touchmove', handleTouchMove);
			sliderContainer.removeEventListener('touchend', handleTouchEnd);
			sliderContainer.removeEventListener('mousedown', handleMouseDown);
		}
		window.removeEventListener('mousemove', handleMouseMove);
		window.removeEventListener('mouseup', handleMouseUp);
	});
</script>

<svelte:head>
	{#if heroItems.length > 0}
		<link rel="preload" as="image" href={heroItems[0].latest_episode.thumbnail} />
	{/if}
</svelte:head>

<section class="relative w-full hero no-select" aria-label="Featured anime hero section">
	<!-- Discord server notification -->
	{#if showDiscordNotification}
		<div class="relative p-3 font-medium text-center text-gray-200 bg-gray-800/90 discord-notification" transition:slide={{ duration: 300 }}>
			<span class="text-sm md:text-base">Zapraszamy na nasz nowy serwer <a href="https://discord.gg/lycoriscafe" data-umami-event="openDiscordHero" target="_blank" rel="noopener noreferrer" class="font-semibold text-[#8ec3f4] underline transition-colors hover:text-[#ee8585]">discord.gg/lycoriscafe</a>! Poprzedni niestety spadł :(</span>
			<button
				on:click={dismissDiscordNotification}
				class="absolute p-1 text-gray-400 transition-colors rounded-full top-2 right-2 hover:text-white hover:bg-gray-700"
				aria-label="Zamknij powiadomienie"
			>
				<X class="w-4 h-4 cursor-pointer" />
			</button>
		</div>
	{/if}

	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div class="slider-container" on:touchstart={handleTouchStart} on:touchmove={handleTouchMove} on:touchend={handleTouchEnd} on:mousedown={handleMouseDown}>
		<div class="slider-wrapper" style="transform: translateX(-{currentSlide * 100}%)">
			{#each heroItems as anime, index}
				<div class="slider-slide">
					<div
						class="relative flex h-[400px] cursor-default flex-col justify-end text-white transition-all duration-300 ease-in-out md:h-[60vh] md:min-h-[450px]"
						on:contextmenu|preventDefault={() => handleContextMenu(anime)}
						role="button"
						tabindex="0"
						on:keydown={(event) => handleKeyDown(event, anime, anime.released_episodes)}
					>
						<div class="absolute inset-0 w-full h-full">
							{#if !imageLoadingStates.get(index)}
								<Skeleton class="absolute inset-0 w-full h-full" style="aspect-ratio: 16 / 9;" />
							{/if}
							<img
								src={anime.latest_episode.thumbnail}
								alt={`${anime.title} thumbnail`}
								class="h-full w-full object-cover pb-10 {imageLoadingStates.get(index) ? 'opacity-100' : 'opacity-0'}"
								loading={index === 0 ? 'eager' : 'lazy'}
								fetchpriority={index === 0 ? 'high' : 'low'}
								decoding={index === 0 ? 'sync' : 'async'}
								style="aspect-ratio: 16 / 9; transition: opacity 300ms ease-in-out;"
								on:load={() => handleImageLoad(index)}
							/>
						</div>
						<div class="absolute inset-0 bg-linear-to-t from-gray-900 via-gray-900/60 to-transparent"></div>
						<div class="relative z-10 flex flex-col items-start p-4 space-y-4 md:hidden" style="opacity: {imageLoadingStates.get(index) ? '1' : '0'}; transition: opacity 300ms ease-in-out;">
							<span class="rounded-full bg-[#8ec3f4] px-3 py-1 text-xs font-bold tracking-wider text-black uppercase">
								Odcinek {anime.released_episodes}
							</span>

							<h2 class="text-2xl font-bold break-words transition-all duration-300 ease-in-out truncate-title">
								{getPreferredTitle(anime, preferRomaji)}
							</h2>

							<div class="flex items-center justify-center w-full space-x-4 text-xs md:justify-start">
								<div class="flex items-center">
									<Star class="w-4 h-4 mr-1 text-yellow-400 opacity-90" />
									<span class="opacity-80">
										{#if anime.rating}
											{anime.rating.toFixed(2)}
										{/if}
									</span>
								</div>
								<div class="flex items-center">
									<TrendingUp class="w-4 h-4 mr-1 text-blue-400 opacity-90" />
									<span class="opacity-80">
										{(anime.popularity * 3).toLocaleString()}
										<span class="hidden md:inline">
											{#if anime.heroReason}
												<span class="text-green-400">({anime.heroReason})</span>
											{/if}
										</span>
									</span>
								</div>
								<div class="flex items-center pr-2">
									<List class="w-4 h-4 mr-1 text-rose-400 opacity-70" />
									<span class="whitespace-nowrap opacity-70">Odcinki: {anime.total_episodes ? anime.total_episodes : 'nieznane'}</span>
								</div>
							</div>

							<div class="flex w-full space-x-2">
								<a href={`${generateAnimeUrl(anime)}/watch/${anime.released_episodes}`} class="grow">
									<Button
										class="{showMobileInfo
											? 'mb-0'
											: 'mb-8'} w-full grow cursor-pointer rounded-full bg-[#ee8585] py-2 text-sm font-extrabold text-black transition-all duration-300 hover:bg-[#8ec3f4]"
									>
										<Play class="w-4 h-4 mr-2" />
										Oglądaj teraz
									</Button>
								</a>
								<Button
									on:click={toggleMobileInfo}
									class="p-2 text-white transition-all duration-300 bg-gray-700 rounded-full hover:bg-gray-600"
									aria-label="Pokaż więcej informacji"
									aria-expanded={showMobileInfo}
								>
									<Info class="w-5 h-5" />
								</Button>
							</div>

							{#if showMobileInfo}
								<div transition:slide={{ duration: 300 }} class="w-full p-4 bg-black rounded-lg opacity-80">
									<p class="mb-2 text-sm text-gray-300">
										{@html anime.short_synopsis}
									</p>
									<div class="flex flex-wrap gap-2 mb-2">
										{#each anime.genres as genre}
											<span class="px-2 py-1 text-xs bg-gray-800 rounded-full">
												{genreTranslations[genre] || genre}
											</span>
										{/each}
									</div>
								</div>
							{/if}
						</div>

						<div
							class="relative z-10 flex-col items-start hidden p-6 space-y-4 transition-all duration-300 ease-in-out md:flex"
							style="opacity: {imageLoadingStates.get(index) ? '1' : '0'}; transition: opacity 300ms ease-in-out;"
						>
							<span class="rounded-full bg-[#8ec3f4] px-3 py-1 text-xs font-bold tracking-wider text-black uppercase">
								Odcinek {anime.released_episodes}
							</span>

							<h2 class="truncate-title w-[65vw] text-3xl font-bold break-words opacity-90 transition-all duration-300 ease-in-out sm:text-4xl md:text-5xl lg:text-6xl xl:w-[49vw]">
								{getPreferredTitle(anime, preferRomaji)}
							</h2>

							<div class="flex items-center space-x-4 text-sm">
								<div class="flex items-center">
									<Star class="w-4 h-4 mr-1 text-yellow-400 opacity-90" />
									<span class="opacity-80">
										{#if anime.rating}
											{anime.rating.toFixed(2)}
										{/if}
										{#if anime.rankings?.raw && Array.isArray(anime.rankings.raw)}
											{#if anime.rankings.raw.find((item) => item.type === 'POPULAR' && item.allTime === false && item.season !== null && item.year !== null && item.context === 'most popular')}
												<span class="text-gray-400">
													(#{anime.rankings.raw.find((item) => item.type === 'POPULAR' && item.allTime === false && item.season !== null && item.year !== null && item.context === 'most popular').rank}
													W tym sezonie)
												</span>
											{/if}
										{/if}
									</span>
								</div>
								<div class="flex items-center">
									<TrendingUp class="w-4 h-4 mr-1 text-blue-400 opacity-90" />
									<span class="opacity-80">
										{(anime.popularity * 3).toLocaleString()}
										{#if anime.heroReason}
											<span class="text-green-400">({anime.heroReason})</span>
										{/if}
									</span>
								</div>
								<div class="flex items-center">
									<List class="w-4 h-4 mr-1 text-rose-400 opacity-70" />
									<span class="opacity-70">Odcinki: {anime.total_episodes ? anime.total_episodes : 'nieznane'}</span>
								</div>
							</div>

							<div class="flex flex-wrap gap-2">
								{#each anime.genres as genre}
									<span class="px-2 py-1 text-xs bg-gray-800 rounded-full">
										{genreTranslations[genre] || genre}
									</span>
								{/each}
							</div>

							<p class="max-w-xl text-sm text-gray-300">
								{@html anime.short_synopsis}
							</p>

							<div class="flex mt-4">
								<a href={`${generateAnimeUrl(anime)}/watch/${anime.released_episodes}`} class="contents">
									<Button class="mr-4 cursor-pointer rounded-full bg-[#ee8585] px-6 py-2 text-base font-bold text-black transition-all duration-300 hover:bg-[#8ec3f4]">
										<Play class="w-5 h-5 mr-2" />
										Oglądaj teraz
									</Button>
								</a>
								<a href={generateAnimeUrl(anime)} class="contents">
									<Button class="px-6 py-2 text-white transition-all duration-300 bg-gray-700 rounded-full cursor-pointer hover:bg-gray-600">
										<Info class="w-5 h-5 mr-2" />
										Więcej informacji
									</Button>
								</a>
							</div>
						</div>

						<div class="absolute bottom-0 left-0 right-0 h-48 pointer-events-none bg-linear-to-b from-transparent via-gray-900/70 to-gray-900"></div>
						<div class="absolute left-0 right-0 h-24 pointer-events-none -bottom-12 bg-gray-900/10 backdrop-blur-xs"></div>
					</div>
				</div>
			{/each}
		</div>

		<div class="slider-progress">
			{#each heroItems as _, index}
				<button class="progress-item" class:active={index === currentSlide} on:click={() => goToSlide(index)} aria-label="Go to slide {index + 1}">
					<div class="progress-bar" style="width: {index === currentSlide ? progressValue : 0}%"></div>
				</button>
			{/each}
		</div>
	</div>
</section>

<style>
	.slider-container {
		position: relative;
		width: 100%;
		height: 400px; /* Default for mobile */
		overflow: hidden;
		touch-action: pan-y;
	}

	.hero {
		margin-bottom: -5rem;
		overflow: hidden;
	}

	.slider-wrapper {
		display: flex;
		transition: transform 0.5s ease-in-out;
		height: 100%;
	}

	.slider-slide {
		flex: 0 0 100%;
		width: 100%;
		height: 100%;
	}

	/* 	.slider-controls {
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		transform: translateY(-50%);
		display: flex;
		justify-content: space-between;
		padding: 0 20px;
		z-index: 2;
	}

	.slider-control {
		background: rgba(0, 0, 0, 0.5);
		color: white;
		border: none;
		padding: 10px;
		cursor: pointer;
		transition: background-color 0.3s ease;
	}

	.slider-control:hover {
		background: rgba(0, 0, 0, 0.7);
	} */

	.slider-progress {
		position: absolute;
		bottom: 20px;
		left: 0;
		right: 0;
		display: flex;
		justify-content: center;
		z-index: 2;
	}

	.progress-item {
		width: 50px;
		height: 8px;
		background: rgba(255, 255, 255, 0.15);
		margin: 0 5px;
		border-radius: 9999px;
		overflow: hidden;
		border: none;
		padding: 0;
		cursor: pointer;
		transition: background-color 0.2s ease;
	}

	.progress-item:hover {
		background: rgba(255, 255, 255, 0.25);
	}

	.progress-item:active {
		background: rgba(255, 255, 255, 0.3);
	}

	.progress-bar {
		height: 100%;
		background: white;
		width: 0;
		transition: width 0.1s linear;
	}

	@media (max-width: 768px) {
		.progress-item {
			width: 30px;
		}
	}

	@media (min-width: 768px) {
		.slider-progress {
			position: absolute;
			bottom: 0px;
			left: 0;
			right: 0;
			display: flex;
			justify-content: center;
			z-index: 2;
		}
	}

	@media (min-width: 768px) {
		.slider-container {
			height: 60vh;
			min-height: 450px;
		}
	}

	@media (min-width: 640px) {
		.truncate-title {
			max-width: 80vw;
		}
	}

	@media (min-width: 1024px) {
		.truncate-title {
			max-width: 70vw;
		}
	}

	@media (min-width: 1280px) {
		.truncate-title {
			max-width: 60vw;
		}
	}

	.truncate-title {
		display: -webkit-box;
		line-clamp: 2;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
		text-overflow: ellipsis;
		max-width: 90vw;
	}

	:global(.no-select) {
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
	}

	.discord-notification {
		position: relative;
		z-index: 20;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
		backdrop-filter: blur(8px);
		border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	}
</style>
